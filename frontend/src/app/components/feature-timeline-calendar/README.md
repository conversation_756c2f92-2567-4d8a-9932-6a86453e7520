# Feature Timeline Calendar Component

## Description

Ce composant affiche une vue calendaire des Features et User Stories avec une timeline horizontale. Il remplace les anciens composants calendaires pour offrir une meilleure expérience utilisateur.

## Fonctionnalités

### Structure
- **Colonne Features fixe** : Affiche la liste des Features avec leurs informations (titre, ID, état)
- **Timeline scrollable** : Affiche les jours ouvrés (lundi-vendredi) avec scroll horizontal
- **User Stories** : Barres horizontales colorées représentant la durée des User Stories

### Caractéristiques
- ✅ Affichage lundi-vendredi uniquement (pas de weekends)
- ✅ Colonne Features fixe horizontalement (ne scroll pas avec la timeline)
- ✅ Scroll synchronisé entre l'en-tête et le corps de la timeline
- ✅ Couleurs par Feature (toutes les US d'une Feature ont la même couleur)
- ✅ Gestion des chevauchements (User Stories sur plusieurs lignes si nécessaire)
- ✅ Informations détaillées au survol (titre + assignation)
- ✅ Design ergonomique avec grille visible

### Données affichées
- **Features** : Titre, ID, État
- **User Stories** : Titre, Assignation, Durée visuelle
- **Timeline** : Jours ouvrés avec indication du jour actuel

## Utilisation

```html
<app-feature-timeline-calendar
  [sprint]="selectedSprint"
  [workItems]="workItems">
</app-feature-timeline-calendar>
```

## Inputs

- `sprint: Sprint | null` - Le sprint sélectionné
- `workItems: WorkItem[]` - Liste des work items (Features et User Stories)

## Configuration

Le composant utilise une configuration par défaut :
- Largeur des jours : 120px
- Hauteur des lignes : 60px
- Largeur colonne Features : 300px

## Styles

Le composant utilise des couleurs Microsoft Fluent Design :
- Bleu Microsoft (#0078d4)
- Vert (#107c10)
- Rouge (#d13438)
- Violet (#8764b8)
- Orange (#ca5010)
- Teal (#038387)
- Lavande (#8e8cd8)
- Turquoise (#00b7c3)
- Vert lime (#bad80a)

## Dépendances

- Angular 19+
