<div class="feature-timeline-container" *ngIf="sprint && workItems.length > 0">

  <!-- Grille unifiée avec CSS Grid -->
  <div class="unified-calendar-grid" #unifiedGrid>

    <!-- En-tête Features -->
    <div class="grid-header-features">
      <h3>Features</h3>
    </div>

    <!-- En-têtes des jours -->
    <div class="grid-header-day"
         *ngFor="let day of timelineDays"
         [class.today]="day.isToday">
      <div class="day-name">{{ day.dayName }}</div>
      <div class="day-date">{{ day.dayNumber }}</div>
    </div>

    <!-- Lignes de features et timeline -->
    <ng-container *ngFor="let featureRow of featureRows">

      <!-- Cellule Feature -->
      <div class="grid-feature-cell"
           [style]="getFeatureCellStyle(featureRow)">
        <div class="feature-info">
          <div class="feature-title">{{ featureRow.feature.fields['System.Title'] }}</div>
          <div class="feature-id">#{{ featureRow.feature.id }}</div>
          <div class="feature-sprint" *ngIf="featureRow.feature.fields['System.IterationPath']">
            Sprint : {{ getFeatureSprintName(featureRow.feature.fields['System.IterationPath']) }}
          </div>
          <div class="feature-state">{{ featureRow.feature.fields['System.State'] }}</div>
        </div>
      </div>

      <!-- Cellules de timeline pour cette feature -->
      <div class="grid-timeline-cell"
           *ngFor="let day of timelineDays"
           [class.today]="day.isToday">

        <!-- User Stories pour ce jour spécifique (seulement le premier jour de chaque US) -->
        <div class="user-story-bar"
             *ngFor="let userStory of getUserStoriesStartingOnDay(featureRow, day)"
             [style]="getUserStoryContinuousStyle(userStory)"
             [appUserStoryTooltip]="getUserStoryWorkItem(userStory)">

          <div class="user-story-content">
            <div class="user-story-title">{{ userStory.title }}</div>
            <div class="user-story-assignee">{{ userStory.assignedTo }}</div>
            <div class="user-story-state">{{ userStory.state }}</div>
          </div>
        </div>
      </div>

    </ng-container>

  </div>


</div>

<!-- Message si pas de données -->
<div class="no-data-message" *ngIf="!sprint || workItems.length === 0">
  <div class="no-data-content">
    <h3>Aucune donnée à afficher</h3>
    <p *ngIf="!sprint">Veuillez sélectionner un sprint.</p>
    <p *ngIf="sprint && workItems.length === 0">Aucun élément trouvé pour ce sprint.</p>
  </div>
</div>

<!-- Pied de page fixe avec informations du sprint -->
<div class="sprint-footer" *ngIf="sprint">
  <div class="sprint-details">
    <div class="sprint-name">{{ sprint.name }}</div>
    <div class="sprint-dates">
      {{ sprint.attributes.startDate | date:'dd/MM/yyyy' }} -
      {{ sprint.attributes.finishDate | date:'dd/MM/yyyy' }}
    </div>
  </div>
  <div class="sprint-actions">
    <button
      class="today-btn"
      (click)="scrollToToday()"
      [disabled]="!isTodayVisible"
      [title]="isTodayVisible ? 'Aller à la date d\'aujourd\'hui' : 'La date d\'aujourd\'hui n\'est pas dans ce sprint'">
      📅 Aujourd'hui
    </button>
  </div>
</div>
