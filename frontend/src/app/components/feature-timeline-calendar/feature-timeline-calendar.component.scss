// Import des variables globales
@use '../../../styles/variables' as vars;

.feature-timeline-container {
  display: flex;
  flex-direction: column;
  // Utiliser les variables CSS définies dans app.component.scss pour une hauteur exacte
  // Cela garantit que le conteneur se termine exactement où commence le footer
  height: calc(100vh - var(--header-height) - var(--footer-height));
  max-height: calc(100vh - var(--header-height) - var(--footer-height));
  background: vars.$background-white;
  border: vars.$border-width-thin solid vars.$border-color;
  border-radius: vars.$border-radius-md;
  overflow: hidden;
  font-family: vars.$font-family;
  // S'assurer qu'il n'y a pas de débordement sous le footer
  margin-bottom: 0;
  position: relative;
  // Assurer que le conteneur ne dépasse jamais la zone disponible
  box-sizing: border-box;
}

// Grille unifiée avec CSS Grid - BEAUCOUP plus performant !
.unified-calendar-grid {
  display: grid;
  height: 100%;
  overflow: auto;

  // Configuration de la grille : colonne fixe + colonnes dynamiques
  grid-template-columns: vars.$feature-width repeat(var(--timeline-days-count), vars.$day-width);
  // Utiliser les hauteurs calculées dynamiquement au lieu de grid-auto-rows
  grid-template-rows: var(--grid-row-heights, 60px auto);

  // Optimisations pour les performances
  will-change: scroll-position;
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;

  // Bordures de la grille
  gap: 0;
  border-collapse: collapse;

  // S'assurer que les barres de scroll sont visibles et bien positionnées
  // La barre de scroll horizontale doit être visible au-dessus du footer
  scrollbar-width: thin; // Firefox
  scrollbar-color: #c1c1c1 #f1f1f1; // Firefox

  // Webkit scrollbars (Chrome, Safari, Edge) - plus visibles
  &::-webkit-scrollbar {
    height: 18px; // Hauteur augmentée pour la scrollbar horizontale
    width: 18px;  // Largeur augmentée pour la scrollbar verticale
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 9px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 9px;
    border: 3px solid #f1f1f1;

    &:hover {
      background: #a8a8a8;
    }
  }

  &::-webkit-scrollbar-corner {
    background: #f1f1f1;
  }

  // Assurer que la grille respecte exactement les limites du conteneur
  box-sizing: border-box;
  // Empêcher tout débordement qui pourrait cacher les scroll bars
  max-height: 100%;
}

// En-tête Features (position fixe)
.grid-header-features {
  position: sticky;
  top: 0;
  left: 0;
  z-index: vars.$z-index-header;
  background: vars.$header-bg;
  border-right: vars.$border-width-thick solid vars.$border-color;
  border-bottom: vars.$border-width-thick solid vars.$border-color;
  padding: vars.$spacing-lg;
  display: flex;
  align-items: center;
  justify-content: center;

  h3 {
    margin: 0;
    font-size: vars.$font-size-large;
    font-weight: vars.$font-weight-semibold;
    color: vars.$text-dark;
  }
}

// En-têtes des jours (sticky top)
.grid-header-day {
  position: sticky;
  top: 0;
  z-index: 15;
  background: #f8f9fa;
  border-right: 1px solid #e1e1e1;
  border-bottom: 2px solid #e1e1e1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &.today {
    background: #e3f2fd;
    border-left: 2px solid #2196f3;
    border-right: 2px solid #2196f3;
  }

  .day-name {
    font-size: 12px;
    font-weight: 600;
    color: #605e5c;
    text-transform: uppercase;
  }

  .day-date {
    font-size: 14px;
    font-weight: 500;
    color: #323130;
    margin-top: 2px;
  }
}

// Cellules Features (sticky left)
.grid-feature-cell {
  position: sticky;
  left: 0;
  z-index: 15; // Plus élevé que les User Stories pour rester au-dessus
  background: #fafafa;
  border-right: 2px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
  border-left: 4px solid #0078d4;
  padding: 15px;
  display: flex;
  // Supprimer min-height car géré par CSS Grid
}

.feature-info {
  width: 100%;

  .feature-title {
    font-size: 14px;
    font-weight: 600;
    color: #323130;
    margin-bottom: 8px;
    line-height: 1.3;
    word-wrap: break-word;
  }

  .feature-id {
    font-size: 12px;
    color: #605e5c;
    margin-bottom: 4px;
  }

  .feature-sprint {
    font-size: 11px;
    color: #8a8886;
    margin-bottom: 4px;
    font-style: italic;
  }

  .feature-state {
    font-size: 11px;
    color: #0078d4;
    font-weight: 500;
    text-transform: uppercase;
  }
}

// Cellules de timeline
.grid-timeline-cell {
  background: #ffffff;
  border-right: 1px solid #e1e1e1;
  border-bottom: 1px solid #e1e1e1;
  position: relative; // Nécessaire pour le positionnement absolu des User Stories
  // Supprimer min-height car géré par CSS Grid
  overflow: visible; // Permettre aux User Stories de déborder sur les cellules suivantes

  &.today {
    background: #f0f8ff;
    border-left: 2px solid #2196f3;
    border-right: 2px solid #2196f3;
  }
}

// User Stories dans la grille
.user-story-bar {
  position: absolute;
  height: 42px; // Augmenté pour accommoder 3 lignes
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10; // Plus élevé pour être au-dessus des cellules mais sous les features

  // Ensure minimum width for single-day User Stories
  min-width: calc(#{vars.$day-width} - 8px); // Largeur du jour moins padding

  // Ensure text is visible even for short bars
  overflow: hidden;

  &:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    z-index: 12; // Encore plus élevé au hover
  }
}

.user-story-content {
  padding: 3px 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  overflow: hidden;

  .user-story-title {
    font-size: 11px;
    font-weight: 600;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .user-story-assignee {
    font-size: 9px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.1;
    margin-bottom: 1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .user-story-state {
    font-size: 9px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-style: italic;
  }
}



// Sprint footer sticky
.sprint-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  // Utiliser la variable CSS pour une cohérence parfaite avec le calcul de hauteur du conteneur
  height: var(--footer-height);
  min-height: var(--footer-height);
  background: #f8f9fa;
  border-top: 2px solid #e1e1e1;
  padding: 12px 20px;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box; // Important : inclure padding et border dans la hauteur

  .sprint-details {
    display: flex;
    align-items: center;
    gap: 20px;

    .sprint-name {
      font-size: 14px;
      font-weight: 600;
      color: #323130;
    }

    .sprint-dates {
      font-size: 12px;
      color: #605e5c;
    }
  }

  .sprint-actions {
    display: flex;
    align-items: center;
    gap: 10px;

    .today-btn {
      background: vars.$primary-color;
      color: white;
      border: none;
      border-radius: vars.$border-radius-md;
      padding: 8px vars.$spacing-lg;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all vars.$transition-normal;
      box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);

      &:hover:not(:disabled) {
        background: #106ebe;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 120, 212, 0.4);
      }

      &:active:not(:disabled) {
        transform: translateY(0);
      }

      &:disabled {
        background: #c8c6c4;
        color: #605e5c;
        cursor: not-allowed;
        box-shadow: none;
      }
    }
  }
}

// Variables CSS pour la grille dynamique
:host {
  --timeline-days-count: 50; // Sera mis à jour dynamiquement
  --grid-row-heights: 60px auto; // Hauteurs des lignes, sera mis à jour dynamiquement
}
