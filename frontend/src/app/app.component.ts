import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {Sprint} from './interfaces/sprint';
import {WorkItem} from './interfaces/work-item';
import {FeatureTimelineCalendarComponent} from './components/feature-timeline-calendar/feature-timeline-calendar.component';
import {PlanifyDataService} from './services/planify-data.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, FormsModule, FeatureTimelineCalendarComponent],

  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  protected sprints: Sprint[] = [];
  protected isLoading = true;
  protected errorMessage: string | null = null;
  protected selectedSprintPath: string = '';
  protected selectedSprintFeatures: WorkItem[] = [];
  protected selectedSprint: Sprint | null = null;

  constructor(private planifyDataService: PlanifyDataService) {}

  ngOnInit(): void {
    this.fetchSprints();
  }

  protected fetchSprints(): void {
    this.isLoading = true;
    this.errorMessage = null;

    this.planifyDataService.getSprints().subscribe({
      next: (responseSprints) => {
        this.sprints = responseSprints;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Erreur lors de la récupération des sprints: ${error.message}`;
        this.isLoading = false;
      }
    });
  }

  protected onSprintChange(): void {
    this.selectedSprintFeatures = [];
    this.selectedSprint = null;

    if (!this.selectedSprintPath) return;

    // Trouver le sprint sélectionné
    this.selectedSprint = this.sprints.find(s => s.path === this.selectedSprintPath) || null;

    this.isLoading = true;

    this.planifyDataService.getSprintFeatures(this.selectedSprintPath).subscribe({
      next: (items) => {
        this.selectedSprintFeatures = items;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Erreur lors de la récupération des features/US: ${error.message}`;
        this.isLoading = false;
      }
    });
  }

  protected refreshPage(): void {
    window.location.reload();
  }

}
