import { TestBed, ComponentFixture } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormsModule } from '@angular/forms';
import { of, throwError, NEVER } from 'rxjs';

import { AppComponent } from './app.component';
import { PlanifyDataService } from './services/planify-data.service';
import { Sprint } from './interfaces/sprint';
import { WorkItem } from './interfaces/work-item';

/**
 * Tests unitaires pour AppComponent
 *
 * COUVERTURE MÉTIER :
 * - Orchestration de la planification
 * - Gestion des états d'interface (chargement, erreur, succès)
 * - Sélection et changement de sprint
 * - Intégration avec le service de données
 *
 * JUSTIFICATION :
 * Ces tests couvrent la logique métier principale de l'application :
 * le workflow de sélection de sprint et l'affichage des données de planification.
 */

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let mockService: jasmine.SpyObj<PlanifyDataService>;

  const mockSprints: Sprint[] = [
    {
      id: 'sprint-1',
      name: 'Sprint 1',
      path: 'Project\\Sprint 1',
      attributes: {
        startDate: '2024-01-01T00:00:00Z',
        finishDate: '2024-01-14T23:59:59Z',
        timeFrame: 'current'
      },
      url: 'http://test.com/sprint1'
    },
    {
      id: 'sprint-2',
      name: 'Sprint 2',
      path: 'Project\\Sprint 2',
      attributes: {
        startDate: '2024-01-15T00:00:00Z',
        finishDate: '2024-01-28T23:59:59Z',
        timeFrame: 'future'
      },
      url: 'http://test.com/sprint2'
    }
  ];

  const mockWorkItems: WorkItem[] = [
    {
      id: 1,
      rev: 1,
      fields: {
        'System.Id': 1,
        'System.Title': 'Feature Authentification',
        'System.WorkItemType': 'Feature',
        'System.State': 'Active',
        'System.CreatedDate': '2024-01-01T00:00:00Z',
        'System.IterationPath': 'Project\\Sprint 1'
      },
      url: 'http://test.com/workitem1'
    }
  ];

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('PlanifyDataService', ['getSprints', 'getSprintFeatures']);

    await TestBed.configureTestingModule({
      imports: [AppComponent, HttpClientTestingModule, FormsModule],
      providers: [{ provide: PlanifyDataService, useValue: spy }]
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    mockService = TestBed.inject(PlanifyDataService) as jasmine.SpyObj<PlanifyDataService>;

    mockService.getSprints.and.returnValue(of(mockSprints));
    mockService.getSprintFeatures.and.returnValue(of(mockWorkItems));
  });

  /**
   * PERTINENCE : Initialisation correcte de l'état de l'application,
   * essentiel pour l'UX de chargement
   */
  it('devrait initialiser l\'état de chargement au démarrage', () => {
    expect(component['isLoading']).toBe(true);
    expect(component['selectedSprintPath']).toBe('');
    expect(component['sprints']).toEqual([]);
    expect(component['selectedSprint']).toBeNull();
  });

  /**
   * PERTINENCE : Workflow principal - chargement automatique des sprints
   * au démarrage de l'application
   */
  it('devrait charger automatiquement les sprints disponibles', () => {
    component.ngOnInit();
    expect(mockService.getSprints).toHaveBeenCalled();
  });

  /**
   * PERTINENCE : Gestion d'état après chargement réussi des sprints,
   * permet à l'utilisateur de sélectionner un sprint
   */
  it('devrait mettre à jour l\'état après chargement des sprints', () => {
    component['fetchSprints']();

    expect(component['sprints']).toEqual(mockSprints);
    expect(component['isLoading']).toBe(false);
    expect(component['errorMessage']).toBeNull();
  });

  /**
   * PERTINENCE : Gestion d'erreur critique - l'utilisateur doit être informé
   * si les sprints ne peuvent pas être chargés
   */
  it('devrait afficher une erreur si le chargement des sprints échoue', () => {
    mockService.getSprints.and.returnValue(throwError(() => new Error('Erreur réseau')));

    component['fetchSprints']();

    expect(component['errorMessage']).toContain('Erreur réseau');
    expect(component['isLoading']).toBe(false);
    expect(component['sprints']).toEqual([]);
  });

  /**
   * PERTINENCE : Workflow de sélection de sprint - fonctionnalité centrale
   * qui déclenche l'affichage de la timeline de planification
   */
  it('devrait charger les données du sprint sélectionné', () => {
    component['sprints'] = mockSprints;
    component['selectedSprintPath'] = 'Project\\Sprint 1';

    component['onSprintChange']();

    expect(component['selectedSprint']).toEqual(mockSprints[0]);
    expect(mockService.getSprintFeatures).toHaveBeenCalledWith('Project\\Sprint 1');
    expect(component['selectedSprintFeatures']).toEqual(mockWorkItems);
  });

  /**
   * PERTINENCE : Validation que l'interface calendrier est affichée
   * quand toutes les données sont disponibles
   */
  it('devrait activer l\'affichage du calendrier avec les données complètes', () => {
    component['selectedSprint'] = mockSprints[0];
    component['selectedSprintFeatures'] = mockWorkItems;
    component['isLoading'] = false;
    component['errorMessage'] = null;

    // Vérification que les conditions d'affichage du calendrier sont remplies
    expect(component['selectedSprint']).toBeTruthy();
    expect(component['selectedSprintFeatures'].length).toBeGreaterThan(0);
    expect(component['isLoading']).toBe(false);
    expect(component['errorMessage']).toBeNull();
  });
});
