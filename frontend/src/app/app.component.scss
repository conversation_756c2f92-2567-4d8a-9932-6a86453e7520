// Import des variables globales
@use '../styles/variables' as vars;

// Interface principale
.planify-app {
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
  font-family: vars.$font-family;
  overflow: hidden;
  position: relative;

  * {
    font-family: vars.$font-family;
  }

  // Le composant calendrier doit prendre l'espace disponible
  app-feature-timeline-calendar {
    flex: 0 0 auto; // Ne pas utiliser flex: 1 pour éviter les conflits de hauteur
    overflow: hidden;
  }

  .app-header {
    background: white;
    border-bottom: 2px solid vars.$border-color;
    padding: 15px 25px;
    // Hauteur exacte : padding (15px*2) + border (2px) + contenu (~58px) = 90px
    height: 90px;
    min-height: 90px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
    flex-shrink: 0;
    box-sizing: border-box; // Important : inclure padding et border dans la hauteur

    .app-title {
      font-size: 2.2em;
      font-weight: 700;
      color: #333;
      margin: 0;
      letter-spacing: -0.5px;
      font-family: vars.$font-family;
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 20px;

      .sprint-selector-inline {
        display: flex;
        align-items: center;
        gap: 10px;

        label {
          font-weight: 600;
          color: #333;
          font-size: 0.9em;
          font-family: vars.$font-family;
          white-space: nowrap;
        }

        select {
          min-width: 200px;
          padding: 8px 12px;
          font-size: 0.9em;
          font-family: vars.$font-family;
          border: 2px solid vars.$border-color;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          transition: all 0.2s ease;

          &:focus {
            outline: none;
            border-color: vars.$primary-color;
            box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
          }

          &:hover {
            border-color: #c6c8ca;
          }
        }
      }
    }
  }
}

// États de l'application
.loading-state, .error-state, .welcome-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  font-family: vars.$font-family;
}

.loading-state {
  flex-direction: column;
  gap: 15px;
  color: vars.$secondary-color;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid vars.$light-gray;
    border-top: 4px solid vars.$primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  span {
    font-size: 1.1em;
    font-weight: 500;
    font-family: vars.$font-family;
  }
}

.error-state {
  flex-direction: column;
  gap: 15px;
  color: vars.$error-color;
  background: #f8d7da;
  border-radius: 8px;
  margin: 20px;

  .error-icon {
    font-size: 2em;
  }

  span {
    font-size: 1.1em;
    font-weight: 500;
    font-family: vars.$font-family;
  }

  .refresh-button {
    margin-top: 10px;
    padding: 8px 16px;
    background-color: vars.$primary-color;
    color: white;
    border: none;
    border-radius: 6px;
    font-family: vars.$font-family;
    font-size: 0.95em;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    align-self: center;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #106ebe;
    }

    &:active {
      background-color: #005a9e;
    }
  }
}

.welcome-state {
  .welcome-content {
    text-align: center;
    max-width: 500px;

    h2 {
      color: #333;
      margin-bottom: 15px;
      font-size: 1.8em;
      font-weight: 600;
      font-family: vars.$font-family;
    }

    p {
      color: vars.$secondary-color;
      font-size: 1.1em;
      line-height: 1.5;
      font-family: vars.$font-family;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
