# 🎯 Tooltip des User Stories - Documentation

## 📋 Fonctionnalité ajoutée

Le **tooltip détaillé** s'affiche au survol des User Stories dans la timeline. Ce tooltip affiche toutes les informations importantes de l'US directement récupérées depuis les données Azure DevOps.

## 🔧 Composants créés

### 1. **UserStoryTooltipComponent**
- **Fichier** : `frontend/src/app/components/user-story-tooltip/user-story-tooltip.component.ts`
- **Rôle** : Composant qui affiche le contenu du tooltip avec toutes les informations de l'US

### 2. **UserStoryTooltipDirective**
- **Fichier** : `frontend/src/app/directives/user-story-tooltip.directive.ts`
- **Rôle** : Directive qui gère l'affichage/masquage du tooltip au survol

### 3. **Styles du tooltip**
- **Fichier** : `frontend/src/app/components/user-story-tooltip/user-story-tooltip.component.scss`
- **Rôle** : Styles élégants avec animation et positionnement intelligent

## 📊 Informations affichées dans le tooltip

Le tooltip affiche les informations suivantes **récupérées directement des données Azure DevOps** :

### 🏷️ **En-tête**
- **Titre** : `System.Title` - Le titre complet de l'User Story
- **ID** : `System.Id` - L'identifiant unique (#2001, #2002, etc.)

### 📋 **Détails**
- **État** : `System.State` - État actuel (New, Active, Closed, Resolved)
- **Affectation** : `System.AssignedTo.displayName` - Nom de la personne assignée
- **Date de début** : `Microsoft.VSTS.Scheduling.StartDate` - Date de début réelle
- **Date de fin** : `Microsoft.VSTS.Scheduling.FinishDate` - Date de fin réelle
- **Effort** : `Microsoft.VSTS.Scheduling.Effort` - Points d'effort estimés

## 🎨 Design et ergonomie

### **Apparence**
- ✅ **Design moderne** : Fond blanc, bordures arrondies, ombre portée
- ✅ **Flèche de pointage** : Indique clairement l'élément survolé
- ✅ **États colorés** : Chaque état a sa propre couleur (New=bleu, Active=orange, etc.)
- ✅ **Animation fluide** : Apparition en fondu avec légère translation

### **Positionnement intelligent**
- ✅ **Au-dessus par défaut** : Le tooltip apparaît au-dessus de l'US
- ✅ **Repositionnement automatique** : Si pas assez de place en haut, s'affiche en bas
- ✅ **Centrage horizontal** : Centré par rapport à l'US survolée
- ✅ **Évite les débordements** : Reste toujours visible dans la fenêtre

### **Interaction utilisateur**
- ✅ **Délai d'apparition** : 300ms pour éviter les tooltips intempestifs
- ✅ **Délai de disparition** : 100ms pour permettre de passer sur le tooltip
- ✅ **Survol du tooltip** : Reste visible si on passe la souris dessus
- ✅ **Nettoyage automatique** : Suppression propre lors de la destruction du composant

## 🔗 Intégration dans la timeline

### **Template HTML**
```html
<div class="user-story-bar"
     *ngFor="let userStory of featureRow.userStories"
     [style]="getUserStoryStyle(userStory)"
     [appUserStoryTooltip]="getUserStoryWorkItem(userStory)">
  <!-- Contenu de l'US -->
</div>
```

### **Méthode TypeScript**
```typescript
getUserStoryWorkItem(userStory: UserStoryBar): WorkItem | null {
  return userStory.userStory; // Retourne l'objet WorkItem complet
}
```

## 🎯 Utilisation

### **Pour l'utilisateur final**
1. **Survoler une User Story** dans la timeline
2. **Attendre 300ms** - le tooltip apparaît automatiquement
3. **Lire les détails** - toutes les informations importantes sont affichées
4. **Quitter la zone** - le tooltip disparaît après 100ms

### **Informations disponibles**
- ✅ **Titre complet** de l'User Story
- ✅ **Personne assignée** (nom complet)
- ✅ **Dates réelles** de début et fin (pas calculées)
- ✅ **État actuel** avec code couleur
- ✅ **Effort estimé** en points

## 🔧 Configuration technique

### **Données utilisées**
Les données proviennent directement du backend Flask via l'API `/api/work-items/{sprint_id}` :

```json
{
  "id": 2001,
  "fields": {
    "System.Title": "Page de connexion",
    "System.State": "Closed",
    "System.AssignedTo": {
      "displayName": "Marie Martin"
    },
    "Microsoft.VSTS.Scheduling.StartDate": "2025-06-17T00:00:00",
    "Microsoft.VSTS.Scheduling.FinishDate": "2025-06-27T00:00:00",
    "Microsoft.VSTS.Scheduling.Effort": 5
  }
}
```

### **Gestion des erreurs**
- ✅ **Données manquantes** : Affichage de "Non assigné" si pas d'assignation
- ✅ **Dates invalides** : Gestion par le pipe DatePipe d'Angular
- ✅ **Effort manquant** : La ligne n'est pas affichée si pas d'effort défini

## 🚀 Test de la fonctionnalité

### **Pour tester le tooltip**
1. Aller sur **http://localhost:4200**
2. Sélectionner un sprint avec des User Stories
3. **Survoler une barre d'User Story** dans la timeline
4. Observer l'apparition du tooltip avec tous les détails
5. Tester le repositionnement en survolant des US en haut/bas de l'écran

### **Exemples de données visibles**
- **"Page de connexion"** - Assignée à Marie Martin, du 17/06 au 27/06, 5 points
- **"Validation des formulaires"** - Assignée à Pierre Durand, du 30/06 au 05/07, 3 points
- **"Gestion des sessions JWT"** - Assignée à Jean Dupont, du 03/07 au 10/07, 8 points

## ✨ Avantages de cette implémentation

### **Pour l'utilisateur**
- ✅ **Information complète** : Tous les détails en un coup d'œil
- ✅ **Pas de navigation** : Pas besoin de cliquer ou changer de page
- ✅ **Données réelles** : Informations directement depuis Azure DevOps
- ✅ **Interface intuitive** : Survol naturel et ergonomique

### **Pour le développement**
- ✅ **Composant réutilisable** : Peut être utilisé ailleurs dans l'application
- ✅ **Directive flexible** : Facile à appliquer sur d'autres éléments
- ✅ **Performance optimisée** : Création/destruction à la demande
- ✅ **Code maintenable** : Séparation claire des responsabilités

## 🎉 Résultat final

Le tooltip fournit une **expérience utilisateur riche** en affichant instantanément toutes les informations importantes d'une User Story au simple survol, sans interrompre le flux de travail de planification. Les données sont **authentiques** (provenant directement d'Azure DevOps) et l'interface est **élégante** et **responsive**.
