# 🐳 Guide Docker Compose pour Planify

Ce guide vous explique comment utiliser Docker Compose pour lancer facilement le frontend et le backend de Planify.

## 📋 Prérequis

- Docker (version 20.10+)
- Docker Compose (version 2.0+)
- Git

## 🚀 Démarrage rapide

### 1. Configuration

Copiez le fichier de configuration d'exemple :
```bash
cp .env.example .env
```

Éditez le fichier `.env` selon vos besoins :
- **Mode test** : Laissez `AZURE_DEVOPS_PAT` vide pour utiliser des données de démonstration
- **Mode production** : Remplissez toutes les variables Azure DevOps

### 2. Build du frontend (requis)

Avant de lancer Docker Compose, vous devez builder le frontend :
```bash
cd frontend
npm install
npm run build
cd ..
```

### 3. Lancement des services

```bash
# Lancer en mode développement
docker-compose up -d

# Ou avec les logs visibles
docker-compose up
```

### 4. Accès aux applications

- **Frontend** : http://localhost:4200
- **Backend API** : http://localhost:5000
- **Health check** : http://localhost:5000/api/health

## 🔧 Commandes utiles

### Gestion des services
```bash
# Arrêter les services
docker-compose down

# Redémarrer un service spécifique
docker-compose restart planify-backend

# Voir les logs
docker-compose logs -f planify-backend
docker-compose logs -f planify-frontend

# Voir le statut des services
docker-compose ps
```

### Développement
```bash
# Rebuild après modification du Dockerfile
docker-compose build

# Rebuild et redémarrer
docker-compose up --build

# Accéder au shell d'un conteneur
docker-compose exec planify-backend bash
docker-compose exec planify-frontend sh
```

## 🌍 Modes d'exécution

### Mode Développement (par défaut)
- Hot reload activé pour le backend
- Variables d'environnement de développement
- SSL désactivé
- Logs détaillés

### Mode Production
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```
- Gunicorn avec plusieurs workers
- SSL activé
- Configuration optimisée

## 📁 Structure des fichiers

```
.
├── docker-compose.yml          # Configuration principale
├── docker-compose.override.yml # Surcharges pour le développement
├── docker-compose.prod.yml     # Configuration de production
├── .env.example               # Template de configuration
├── backend/
│   └── Dockerfile.dev         # Dockerfile simplifié pour le backend
└── frontend/
    └── Dockerfile.dev         # Dockerfile simplifié pour le frontend
```

## 🔍 Dépannage

### Le frontend ne se charge pas
1. Vérifiez que le build Angular a été fait : `cd frontend && npm run build`
2. Vérifiez les logs : `docker-compose logs planify-frontend`

### Le backend ne répond pas
1. Vérifiez les variables d'environnement dans `.env`
2. Testez le health check : `curl http://localhost:5000/api/health`
3. Vérifiez les logs : `docker-compose logs planify-backend`

### Erreurs de réseau
1. Vérifiez que les ports ne sont pas utilisés : `netstat -tulpn | grep :5000`
2. Redémarrez Docker : `sudo systemctl restart docker`

### Mode test vs production
- **Mode test** : Laissez `AZURE_DEVOPS_PAT` vide dans `.env`
- **Mode production** : Configurez toutes les variables Azure DevOps

## 🔒 Sécurité

- Les conteneurs utilisent des utilisateurs non-root
- Les variables sensibles sont dans `.env` (non versionné)
- SSL activé en production
- Health checks configurés

## 📝 Variables d'environnement

Voir le fichier `.env.example` pour la liste complète des variables disponibles.

### Variables principales :
- `AZURE_DEVOPS_PAT` : Token d'accès (optionnel pour mode test)
- `AZURE_DEVOPS_SERVER_URL` : URL du serveur Azure DevOps
- `DISABLE_SSL_WARNINGS` : true/false pour le développement

## 🆘 Support

En cas de problème :
1. Vérifiez les logs : `docker-compose logs`
2. Vérifiez la configuration : `docker-compose config`
3. Redémarrez les services : `docker-compose restart`
